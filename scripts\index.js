const music = new Audio("audio");

// create Array

// Current player state management
function getCurrentPlayerState() {
  const playerState = localStorage.getItem("currentPlayerState");
  if (playerState) {
    return JSON.parse(playerState);
  }
  return {
    currentSongId: null,
    currentTime: 0,
    isPlaying: false,
    volume: 1,
  };
}

function saveCurrentPlayerState(songId, currentTime = 0, isPlaying = false) {
  const playerState = {
    currentSongId: songId,
    currentTime: currentTime,
    isPlaying: isPlaying,
    volume: music.volume || 1,
    timestamp: Date.now(),
  };
  localStorage.setItem("currentPlayerState", JSON.stringify(playerState));
}

function loadPlayerState() {
  const state = getCurrentPlayerState();
  if (state.currentSongId) {
    // Set the global index to match the saved state
    index = parseInt(state.currentSongId);

    // Update the music player if elements exist
    if (music) {
      music.src = `./audio/${index}.mp3`;
    }

    if (poster_master_play) {
      poster_master_play.src = `./styles/images/img/${index}.jpg`;
    }

    // Find the song in the songs array
    const song = songs.find((song) => song.id === state.currentSongId);
    if (song) {
      if (title) {
        title.innerHTML = song.songName;
      }
      if (download_music) {
        download_music.href = `./audio/${index}.mp3`;
        download_music.setAttribute("download", extractSongName(song.songName));
      }
    }

    // Set the current time if available
    if (state.currentTime > 0 && music) {
      music.currentTime = state.currentTime;
    }

    // Update UI based on playing state
    if (masterPlay && wave) {
      if (state.isPlaying) {
        masterPlay.classList.remove("bi-play-fill");
        masterPlay.classList.add("bi-pause-fill");
        wave.classList.add("active2");
      } else {
        masterPlay.classList.add("bi-play-fill");
        masterPlay.classList.remove("bi-pause-fill");
        wave.classList.remove("active2");
      }
    }

    // Update master heart icon
    if (typeof updateMasterHeartIcon === "function") {
      updateMasterHeartIcon();
    }

    // Update play button states
    if (typeof makeAllPlays === "function") {
      makeAllPlays();
    }

    const playButton = document.getElementById(state.currentSongId);
    if (playButton) {
      playButton.classList.remove("bi-play-circle-fill");
      playButton.classList.add("bi-pause-circle-fill");
    }
  }
}

function extractSongName(songNameHtml) {
  // Extract just the song name without HTML tags
  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = songNameHtml;
  return tempDiv.textContent || tempDiv.innerText || "";
}

// Library functions
function addSongToLibrary(songId) {
  // Get library songs
  const librarySongs = getLibrarySongs();

  // Check if song already exists in library
  if (librarySongs.includes(songId)) {
    return false; // Song already in library
  }

  // Add song to library
  librarySongs.push(songId);

  // Save updated library
  saveLibrarySongs(librarySongs);

  // Show notification
  if (typeof showNotification === "function") {
    showNotification("Thêm vào danh sách thành công", 3000);
  }

  return true; // Song added successfully
}

function removeSongFromLibrary(songId) {
  // Get library songs
  let librarySongs = getLibrarySongs();

  // Remove song from library
  librarySongs = librarySongs.filter((id) => id !== songId);

  // Save updated library
  saveLibrarySongs(librarySongs);

  return true; // Song removed successfully
}

function getLibrarySongs() {
  // Get library songs from localStorage
  const librarySongs = localStorage.getItem("librarySongs");

  // If library songs exist, parse and return them
  if (librarySongs) {
    return JSON.parse(librarySongs);
  }

  // Otherwise, return empty array
  return [];
}

function saveLibrarySongs(songs) {
  // Save library songs to localStorage
  localStorage.setItem("librarySongs", JSON.stringify(songs));
}

function isSongInLibrary(songId) {
  // Get library songs
  const librarySongs = getLibrarySongs();

  // Check if song is in library
  return librarySongs.includes(songId);
}

// Recently played functions
function addSongToRecentlyPlayed(songId) {
  // Get recently played songs
  let recentlyPlayedSongs = getRecentlyPlayedSongs();

  // Create new entry with timestamp
  const newEntry = {
    songId: songId,
    timestamp: new Date().toISOString(),
    playedAt: Date.now(),
  };

  // Remove existing entry if it exists (to avoid duplicates)
  recentlyPlayedSongs = recentlyPlayedSongs.filter(
    (entry) => entry.songId !== songId
  );

  // Add new entry to the beginning
  recentlyPlayedSongs.unshift(newEntry);

  // Limit to last 50 songs to prevent unlimited storage growth
  if (recentlyPlayedSongs.length > 50) {
    recentlyPlayedSongs = recentlyPlayedSongs.slice(0, 50);
  }

  // Save updated recently played songs
  saveRecentlyPlayedSongs(recentlyPlayedSongs);

  return true; // Song added successfully
}

function getRecentlyPlayedSongs() {
  // Get recently played songs from localStorage
  const recentlyPlayedSongs = localStorage.getItem("recentlyPlayedSongs");

  // If recently played songs exist, parse and return them
  if (recentlyPlayedSongs) {
    return JSON.parse(recentlyPlayedSongs);
  }

  // Otherwise, return empty array
  return [];
}

function saveRecentlyPlayedSongs(songs) {
  // Save recently played songs to localStorage
  localStorage.setItem("recentlyPlayedSongs", JSON.stringify(songs));
}

const songs = [
  {
    id: "1",
    songName: `On My Way <br>
        <div class="subtitle">Alan Walker</div>`,
    poster: "./styles/images/img/1.jpg",
  },
  {
    id: "2",
    songName: `Faded <br>
        <div class="subtitle">Alan Walker</div>`,
    poster: "./styles/images/img/2.jpg",
  },
  {
    id: "3",
    songName: `Cartoon - On & On <br>
        <div class="subtitle">Daniel Levi</div>`,
    poster: "./styles/images/img/3.jpg",
  },
  {
    id: "4",
    songName: `Warriyo - Mortals <br>
        <div class="subtitle">Laura Brehm</div>`,
    poster: "./styles/images/img/4.jpg",
  },
  {
    id: "5",
    songName: `Ertugrul Gazi <br>
        <div class="subtitle">Ertugrul</div>`,
    poster: "./styles/images/img/5.jpg",
  },
  {
    id: "6",
    songName: `Electronic Music <br>
        <div class="subtitle">Electro</div>`,
    poster: "./styles/images/img/6.jpg",
  },
  {
    id: "7",
    songName: `Agar Tum Sath Ho <br>
        <div class="subtitle">Tamasha</div>`,
    poster: "./styles/images/img/7.jpg",
  },
  {
    id: "8",
    songName: `Suna Hai <br>
        <div class="subtitle">Neha Kakkar</div>`,
    poster: "./styles/images/img/8.jpg",
  },
  {
    id: "9",
    songName: `Dilbar <br>
        <div class="subtitle">Satyameva Jayate</div>`,
    poster: "./styles/images/img/9.jpg",
  },
  {
    id: "10",
    songName: `Duniya <br>
        <div class="subtitle">Luka Chuppi</div>`,
    poster: "./styles/images/img/10.jpg",
  },
  {
    id: "11",
    songName: `Lagdi Lahore Di <br>
        <div class="subtitle">Street Dancer 3D</div>`,
    poster: "./styles/images/img/11.jpg",
  },
  {
    id: "12",
    songName: `Putt Jatt Da <br>
        <div class="subtitle">Putt Jatt Da</div>`,
    poster: "./styles/images/img/12.jpg",
  },
  {
    id: "13",
    songName: `Baarishein <br>
        <div class="subtitle">Atif Aslam</div>`,
    poster: "./styles/images/img/13.jpg",
  },
  {
    id: "14",
    songName: `Vaaste <br>
        <div class="subtitle">Dhvani Bhanushali</div>`,
    poster: "./styles/images/img/14.jpg",
  },
  {
    id: "15",
    songName: `Lut Gaye <br>
        <div class="subtitle">Jubin Nautiyal</div>`,
    poster: "./styles/images/img/15.jpg",
  },
  {
    id: "16",
    songName: `Tu Meri Zindagi Hai <br>
        <div class="subtitle">Jubin Nautiyal</div>`,
    poster: "./styles/images/img/16.jpg",
  },
];

// Function to create and add library button to song items
function addLibraryButtonToSongItem(element, songId) {
  // Check if button already exists
  if (element.querySelector(".add-to-library")) {
    return;
  }

  // Create library button
  const libraryBtn = document.createElement("i");
  libraryBtn.className = "bi add-to-library";

  // Set appropriate icon based on library status
  if (isSongInLibrary(songId)) {
    libraryBtn.classList.add("bi-heart-fill");
    libraryBtn.title = "Remove from Library";
  } else {
    libraryBtn.classList.add("bi-heart");
    libraryBtn.title = "Add to Library";
  }

  // Add click event to library button
  libraryBtn.addEventListener("click", (e) => {
    e.stopPropagation(); // Prevent navigation to detail page

    if (isSongInLibrary(songId)) {
      // Remove from library
      if (removeSongFromLibrary(songId)) {
        // Update button icon
        libraryBtn.classList.remove("bi-heart-fill");
        libraryBtn.classList.add("bi-heart");
        libraryBtn.title = "Add to Library";

        // Update master heart if this is the currently playing song
        if (index && index.toString() === songId) {
          updateMasterHeartIcon();
        }
      }
    } else {
      // Add to library
      if (addSongToLibrary(songId)) {
        // Update button icon
        libraryBtn.classList.remove("bi-heart");
        libraryBtn.classList.add("bi-heart-fill");
        libraryBtn.title = "Remove from Library";

        // Update master heart if this is the currently playing song
        if (index && index.toString() === songId) {
          updateMasterHeartIcon();
        }
      }
    }
  });

  // Add button to song item
  element.appendChild(libraryBtn);
}

Array.from(document.getElementsByClassName("songItem")).forEach(
  (Element, i) => {
    Element.getElementsByTagName("img")[0].src = songs[i].poster;
    Element.getElementsByTagName("h5")[0].innerHTML = songs[i].songName;

    // Add library button to song item
    addLibraryButtonToSongItem(Element, songs[i].id);

    // Add click event to navigate to music detail page
    Element.addEventListener("click", (e) => {
      // Only navigate if the click is not on the play button or library button
      if (
        !e.target.classList.contains("playListPlay") &&
        !e.target.classList.contains("add-to-library")
      ) {
        window.location.href = `music-detail.html?id=${songs[i].id}`;
      }
    });
  }
);

// Search functionality moved to search.js

let masterPlay = document.getElementById("masterPlay");
let wave = document.getElementsByClassName("wave")[0];

masterPlay.addEventListener("click", () => {
  if (music.paused || music.currentTime <= 0) {
    music.play();
    masterPlay.classList.remove("bi-play-fill");
    masterPlay.classList.add("bi-pause-fill");
    wave.classList.add("active2");

    // Save current player state as playing
    if (index) {
      saveCurrentPlayerState(index.toString(), music.currentTime, true);
    }
  } else {
    music.pause();
    masterPlay.classList.add("bi-play-fill");
    masterPlay.classList.remove("bi-pause-fill");
    wave.classList.remove("active2");

    // Save current player state as paused
    if (index) {
      saveCurrentPlayerState(index.toString(), music.currentTime, false);
    }
  }
});

const makeAllPlays = () => {
  Array.from(document.getElementsByClassName("playListPlay")).forEach(
    (Element) => {
      Element.classList.add("bi-play-circle-fill");
      Element.classList.remove("bi-pause-circle-fill");
    }
  );
};
const makeAllBackgrounds = () => {
  Array.from(document.getElementsByClassName("songItem")).forEach((Element) => {
    Element.style.background = "rgb(105, 105, 170, 0)";
  });
};

let index = 0;
let poster_master_play = document.getElementById("poster_master_play");
let download_music = document.getElementById("download_music");
let title = document.getElementById("title");

// Master player heart icon functionality
let masterHeart = document.getElementById("masterHeart");

// Function to update master player heart icon based on current song
function updateMasterHeartIcon() {
  if (!masterHeart || !index) return;

  const currentSongId = index.toString();

  if (isSongInLibrary(currentSongId)) {
    masterHeart.classList.remove("bi-heart");
    masterHeart.classList.add("bi-heart-fill");
    masterHeart.title = "Remove from Library";
  } else {
    masterHeart.classList.remove("bi-heart-fill");
    masterHeart.classList.add("bi-heart");
    masterHeart.title = "Add to Library";
  }
}

// Global heart synchronization system
function syncAllHeartIcons(songId) {
  // Update master player heart icon
  updateMasterHeartIcon();

  // Update all music card heart icons
  updateMusicCardHeartIcons(songId);

  // Update music detail page heart icon if on that page
  updateMusicDetailHeartIcon(songId);

  // Trigger custom event for other components to listen
  window.dispatchEvent(
    new CustomEvent("heartIconSync", {
      detail: { songId, isInLibrary: isSongInLibrary(songId) },
    })
  );
}

// Function to update all music card heart icons for a specific song
function updateMusicCardHeartIcons(songId) {
  // Update heart icons in all music cards across the page
  const heartIcons = document.querySelectorAll(
    `.add-to-library[data-id="${songId}"], .add-to-library`
  );

  heartIcons.forEach((icon) => {
    // Check if this icon belongs to the current song
    const iconSongId =
      icon.getAttribute("data-id") ||
      icon.closest(".songItem")?.querySelector(".playListPlay")?.id ||
      icon.parentElement?.querySelector(".playListPlay")?.id;

    if (iconSongId === songId) {
      if (isSongInLibrary(songId)) {
        icon.classList.remove("bi-heart");
        icon.classList.add("bi-heart-fill", "in-library");
        icon.title = "Remove from Library";
        // Add animation effect
        icon.classList.add("heart-animation");
        setTimeout(() => {
          icon.classList.remove("heart-animation");
        }, 600);
      } else {
        icon.classList.remove("bi-heart-fill", "in-library");
        icon.classList.add("bi-heart");
        icon.title = "Add to Library";
      }
    }
  });
}

// Function to update music detail page heart icon
function updateMusicDetailHeartIcon(songId) {
  // This will be called from music-detail.js if on that page
  if (typeof window.updateDetailPageHeartIcon === "function") {
    window.updateDetailPageHeartIcon(songId);
  }
}

// Master heart click handler
if (masterHeart) {
  masterHeart.addEventListener("click", (e) => {
    e.stopPropagation();

    if (!index) return;

    const currentSongId = index.toString();

    if (isSongInLibrary(currentSongId)) {
      // Remove from library
      if (removeSongFromLibrary(currentSongId)) {
        // Sync all heart icons
        syncAllHeartIcons(currentSongId);
      }
    } else {
      // Add to library
      if (addSongToLibrary(currentSongId)) {
        // Sync all heart icons
        syncAllHeartIcons(currentSongId);
      }
    }
  });
}

// Initialize heart icons and event listeners on page load
document.addEventListener("DOMContentLoaded", () => {
  // Load player state first
  loadPlayerState();

  // Set initial state of master heart icon
  if (index) {
    updateMasterHeartIcon();
  }

  // Initialize heart icons for all music cards
  initializeHeartIcons();

  // Listen for heart sync events
  window.addEventListener("heartIconSync", (event) => {
    const { songId } = event.detail;
    updateMusicCardHeartIcons(songId);

    // Update recently played page heart icons if on that page
    if (window.location.pathname.includes("recently-played.html")) {
      updateRecentlyPlayedHeartIcons(songId);
    }

    // Update library page if on that page
    if (window.location.pathname.includes("library.html")) {
      updateLibraryHeartIcons(songId);
    }
  });

  // Global function to update recently played heart icons
  window.updateRecentlyPlayedHeartIcons = function (songId) {
    const heartIcons = document.querySelectorAll(
      `.recently-played-song-item .add-to-library[data-id="${songId}"]`
    );
    heartIcons.forEach((icon) => {
      if (isSongInLibrary(songId)) {
        icon.classList.remove("bi-heart");
        icon.classList.add("bi-heart-fill", "in-library");
        icon.title = "Remove from Library";
      } else {
        icon.classList.remove("bi-heart-fill", "in-library");
        icon.classList.add("bi-heart");
        icon.title = "Add to Library";
      }
    });
  };

  // Global function to update library heart icons
  window.updateLibraryHeartIcons = function (songId) {
    const heartIcons = document.querySelectorAll(
      `.library-song-item .add-to-library[data-id="${songId}"]`
    );
    heartIcons.forEach((icon) => {
      if (isSongInLibrary(songId)) {
        icon.classList.remove("bi-heart");
        icon.classList.add("bi-heart-fill", "in-library");
        icon.title = "Remove from Library";
      } else {
        icon.classList.remove("bi-heart-fill", "in-library");
        icon.classList.add("bi-heart");
        icon.title = "Add to Library";
      }
    });
  };

  // Add event listeners to save state when music plays/pauses
  music.addEventListener("play", () => {
    if (index) {
      saveCurrentPlayerState(index.toString(), music.currentTime, true);
    }
  });

  music.addEventListener("pause", () => {
    if (index) {
      saveCurrentPlayerState(index.toString(), music.currentTime, false);
    }
  });

  // Save current time periodically while playing
  music.addEventListener("timeupdate", () => {
    if (index && !music.paused) {
      // Save state every 5 seconds to avoid too frequent updates
      const now = Date.now();
      const lastSave = music.lastStateSave || 0;
      if (now - lastSave > 5000) {
        saveCurrentPlayerState(index.toString(), music.currentTime, true);
        music.lastStateSave = now;
      }
    }
  });
});

// Initialize heart icon functionality for music cards
function initializeHeartIcons() {
  // Set initial state for all heart icons
  document.querySelectorAll(".add-to-library").forEach((heartIcon) => {
    const songId = heartIcon.getAttribute("data-id");
    if (songId && isSongInLibrary(songId)) {
      heartIcon.classList.remove("bi-heart");
      heartIcon.classList.add("bi-heart-fill", "in-library");
      heartIcon.title = "Remove from Library";
    }
  });

  // Add click event listeners to all heart icons
  document.querySelectorAll(".add-to-library").forEach((heartIcon) => {
    heartIcon.addEventListener("click", (e) => {
      e.stopPropagation();
      const songId = e.target.getAttribute("data-id");

      if (!songId) return;

      if (isSongInLibrary(songId)) {
        // Remove from library
        if (removeSongFromLibrary(songId)) {
          // Sync all heart icons
          syncAllHeartIcons(songId);
        }
      } else {
        // Add to library
        if (addSongToLibrary(songId)) {
          // Sync all heart icons
          syncAllHeartIcons(songId);
        }
      }
    });
  });
}
Array.from(document.getElementsByClassName("playListPlay")).forEach(
  (Element) => {
    Element.addEventListener("click", (e) => {
      index = e.target.id;
      makeAllPlays();
      e.target.classList.remove("bi-play-circle-fill");
      e.target.classList.add("bi-pause-circle-fill");
      music.src = `./audio/${index}.mp3`;
      download_music.href = `./audio/${index}.mp3`;
      poster_master_play.src = `./styles/images/img/${index}.jpg`;
      music.play();
      let song_title = songs.filter((ele) => {
        return ele.id == index;
      });

      song_title.forEach((ele) => {
        let { songName } = ele;
        title.innerHTML = songName;
        download_music.setAttribute("download", extractSongName(songName));
      });

      // Save current player state
      saveCurrentPlayerState(index, 0, true);

      // Add to recently played history
      addSongToRecentlyPlayed(index);

      // Update master heart icon
      updateMasterHeartIcon();

      masterPlay.classList.remove("bi-play-fill");
      masterPlay.classList.add("bi-pause-fill");
      wave.classList.add("active2");

      // music.addEventListener('ended', () => {
      //   masterPlay.classList.add("bi-play-fill");
      //   masterPlay.classList.remove("bi-pause-fill");
      //   wave.classList.remove("active2");
      // })
      makeAllBackgrounds();
      Array.from(document.getElementsByClassName("songItem"))[
        `${index - 1}`
      ].style.background = "rgb(105, 105, 170, .1)";
    });
  }
);

let currentStart = document.getElementById("currentStart");
let currentEnd = document.getElementById("currentEnd");
let seek = document.getElementById("seek");
let bar2 = document.getElementById("bar2");
let dot = document.getElementsByClassName("dot")[0];

music.addEventListener("timeupdate", () => {
  let music_curr = music.currentTime;
  let music_dur = music.duration;

  let min = Math.floor(music_dur / 60);
  let sec = Math.floor(music_dur % 60);
  if (sec < 10) {
    sec = `0${sec}`;
  }
  currentEnd.innerText = `${min}:${sec}`;

  let min1 = Math.floor(music_curr / 60);
  let sec1 = Math.floor(music_curr % 60);
  if (sec1 < 10) {
    sec1 = `0${sec1}`;
  }
  currentStart.innerText = `${min1}:${sec1}`;

  let progressbar = parseInt((music.currentTime / music.duration) * 100);
  seek.value = progressbar;
  let seekbar = seek.value;
  bar2.style.width = `${seekbar}%`;
  dot.style.left = `${seekbar}%`;
});

seek.addEventListener("change", () => {
  music.currentTime = (seek.value * music.duration) / 100;
});

const next_music = () => {
  masterPlay.classList.add("bi-pause-fill");
  wave.classList.add("active2");
  if (index == songs.length) {
    index == 0;
  }
  index++;
  music.src = `./audio/${index}.mp3`;
  download_music.href = `./audio/${index}.mp3`;
  poster_master_play.src = `./styles/images/img/${index}.jpg`;
  music.play();
  let song_title = songs.filter((ele) => {
    return ele.id == index;
  });

  song_title.forEach((ele) => {
    let { songName } = ele;
    title.innerHTML = songName;
    download_music.setAttribute("download", extractSongName(songName));
  });
  makeAllBackgrounds();
  Array.from(document.getElementsByClassName("songItem"))[
    `${index - 1}`
  ].style.background = "rgb(105, 105, 170, .1)";
  makeAllPlays();
  document
    .getElementsByClassName("playListPlay")
    [index - 1].classList.remove("bi-play-circle-fill");
  document
    .getElementsByClassName("playListPlay")
    [index - 1].classList.add("bi-pause-circle-fill");

  // Save current player state
  saveCurrentPlayerState(index.toString(), 0, true);

  // Add to recently played history
  addSongToRecentlyPlayed(index);

  // Update master heart icon
  updateMasterHeartIcon();
};

const repeat_music = () => {
  masterPlay.classList.add("bi-pause-fill");
  wave.classList.add("active2");
  index;
  music.src = `./audio/${index}.mp3`;
  download_music.href = `./audio/${index}.mp3`;
  poster_master_play.src = `./styles/images/img/${index}.jpg`;
  music.play();
  let song_title = songs.filter((ele) => {
    return ele.id == index;
  });

  song_title.forEach((ele) => {
    let { songName } = ele;
    title.innerHTML = songName;
    download_music.setAttribute("download", extractSongName(songName));
  });
  makeAllBackgrounds();
  Array.from(document.getElementsByClassName("songItem"))[
    `${index - 1}`
  ].style.background = "rgb(105, 105, 170, .1)";
  makeAllPlays();
  document
    .getElementsByClassName("playListPlay")
    [index - 1].classList.remove("bi-play-circle-fill");
  document
    .getElementsByClassName("playListPlay")
    [index - 1].classList.add("bi-pause-circle-fill");

  // Save current player state
  saveCurrentPlayerState(index.toString(), 0, true);

  // Add to recently played history
  addSongToRecentlyPlayed(index);

  // Update master heart icon
  updateMasterHeartIcon();
};

const random_music = () => {
  masterPlay.classList.add("bi-pause-fill");
  wave.classList.add("active2");
  if (index == songs.length) {
    index == 0;
  }
  index = Math.floor(Math.random() * songs.length + 1);
  music.src = `./audio/${index}.mp3`;
  download_music.href = `./audio/${index}.mp3`;
  poster_master_play.src = `./styles/images/img/${index}.jpg`;
  music.play();
  let song_title = songs.filter((ele) => {
    return ele.id == index;
  });

  song_title.forEach((ele) => {
    let { songName } = ele;
    title.innerHTML = songName;
    download_music.setAttribute("download", extractSongName(songName));
  });
  makeAllBackgrounds();
  Array.from(document.getElementsByClassName("songItem"))[
    `${index - 1}`
  ].style.background = "rgb(105, 105, 170, .1)";
  makeAllPlays();
  document
    .getElementsByClassName("playListPlay")
    [index - 1].classList.remove("bi-play-circle-fill");
  document
    .getElementsByClassName("playListPlay")
    [index - 1].classList.add("bi-pause-circle-fill");

  // Save current player state
  saveCurrentPlayerState(index.toString(), 0, true);

  // Add to recently played history
  addSongToRecentlyPlayed(index);

  // Update master heart icon
  updateMasterHeartIcon();
};

let shuffle = document.getElementsByClassName("shuffle")[0];

shuffle.addEventListener("click", () => {
  let a = shuffle.innerHTML;

  switch (a) {
    case "next":
      shuffle.classList.add("bi-arrow-repeat");
      shuffle.classList.remove("bi-music-note-beamed");
      shuffle.classList.remove("bi-shuffle");
      shuffle.innerHTML = "repeat";
      break;
    case "repeat":
      shuffle.classList.remove("bi-arrow-repeat");
      shuffle.classList.remove("bi-music-note-beamed");
      shuffle.classList.add("bi-shuffle");
      shuffle.innerHTML = "random";
      break;
    case "random":
      shuffle.classList.remove("bi-arrow-repeat");
      shuffle.classList.add("bi-music-note-beamed");
      shuffle.classList.remove("bi-shuffle");
      shuffle.innerHTML = "next";
      break;
  }
});

music.addEventListener("ended", () => {
  let b = shuffle.innerHTML;

  switch (b) {
    case "repeat":
      repeat_music();
      break;
    case "next":
      next_music();
      break;
    case "random":
      random_music();
      break;
  }
});

let vol_icon = document.getElementById("vol_icon");
let vol = document.getElementById("vol");
let vol_dot = document.getElementById("vol_dot");
let vol_bar = document.getElementsByClassName("vol_bar")[0];

vol.addEventListener("change", () => {
  if (vol.value == 0) {
    vol_icon.classList.remove("bi-volume-down-fill");
    vol_icon.classList.add("bi-volume-mute-fill");
    vol_icon.classList.remove("bi-volume-up-fill");
  }
  if (vol.value > 0) {
    vol_icon.classList.add("bi-volume-down-fill");
    vol_icon.classList.remove("bi-volume-mute-fill");
    vol_icon.classList.remove("bi-volume-up-fill");
  }
  if (vol.value > 50) {
    vol_icon.classList.remove("bi-volume-down-fill");
    vol_icon.classList.remove("bi-volume-mute-fill");
    vol_icon.classList.add("bi-volume-up-fill");
  }

  let vol_a = vol.value;
  vol_bar.style.width = `${vol_a}%`;
  vol_dot.style.left = `${vol_a}%`;
  music.volume = vol_a / 100;
});

let back = document.getElementById("back");
let next = document.getElementById("next");

back.addEventListener("click", () => {
  index -= 1;
  if (index < 1) {
    index = Array.from(document.getElementsByClassName("songItem")).length;
  }
  music.src = `./audio/${index}.mp3`;
  download_music.href = `./audio/${index}.mp3`;
  poster_master_play.src = `./styles/images/img/${index}.jpg`;
  music.play();
  let song_title = songs.filter((ele) => {
    return ele.id == index;
  });

  song_title.forEach((ele) => {
    let { songName } = ele;
    title.innerHTML = songName;
    download_music.setAttribute("download", extractSongName(songName));
  });
  makeAllPlays();

  document.getElementById(`${index}`).classList.remove("bi-play-fill");
  document.getElementById(`${index}`).classList.add("bi-pause-fill");
  makeAllBackgrounds();
  Array.from(document.getElementsByClassName("songItem"))[
    `${index - 1}`
  ].style.background = "rgb(105, 105, 170, .1)";

  // Add to recently played history
  addSongToRecentlyPlayed(index);

  // Update master heart icon
  updateMasterHeartIcon();

  // Save current player state
  saveCurrentPlayerState(index.toString(), 0, true);
});

next.addEventListener("click", () => {
  index -= 0;
  index += 1;
  if (index > Array.from(document.getElementsByClassName("songItem")).length) {
    index = 1;
  }
  music.src = `./audio/${index}.mp3`;
  download_music.href = `./audio/${index}.mp3`;
  poster_master_play.src = `./styles/images/img/${index}.jpg`;
  music.play();
  let song_title = songs.filter((ele) => {
    return ele.id == index;
  });

  song_title.forEach((ele) => {
    let { songName } = ele;
    title.innerHTML = songName;
    download_music.setAttribute("download", extractSongName(songName));
  });
  makeAllPlays();

  document.getElementById(`${index}`).classList.remove("bi-play-fill");
  document.getElementById(`${index}`).classList.add("bi-pause-fill");
  makeAllBackgrounds();
  Array.from(document.getElementsByClassName("songItem"))[
    `${index - 1}`
  ].style.background = "rgb(105, 105, 170, .1)";

  // Add to recently played history
  addSongToRecentlyPlayed(index);

  // Update master heart icon
  updateMasterHeartIcon();

  // Save current player state
  saveCurrentPlayerState(index.toString(), 0, true);
});

// Add click event to popular song items
Array.from(document.querySelectorAll(".pop_song .songItem")).forEach(
  (Element) => {
    // Add library button to song item
    const songId = Element.querySelector(".playListPlay").id;

    // Create library button container
    const actionContainer = document.createElement("div");
    actionContainer.className = "song-actions";

    // Add library button to song item
    const libraryBtn = document.createElement("i");
    libraryBtn.className = "bi add-to-library";
    libraryBtn.setAttribute("data-id", songId);

    // Set appropriate icon based on library status
    if (isSongInLibrary(songId)) {
      libraryBtn.classList.add("bi-heart-fill");
      libraryBtn.title = "Remove from Library";
    } else {
      libraryBtn.classList.add("bi-heart");
      libraryBtn.title = "Add to Library";
    }

    // Add click event to library button
    libraryBtn.addEventListener("click", (e) => {
      e.stopPropagation(); // Prevent navigation to detail page

      if (isSongInLibrary(songId)) {
        // Remove from library
        if (removeSongFromLibrary(songId)) {
          // Sync all heart icons
          syncAllHeartIcons(songId);
        }
      } else {
        // Add to library
        if (addSongToLibrary(songId)) {
          // Sync all heart icons
          syncAllHeartIcons(songId);
        }
      }
    });

    // Add button to container
    actionContainer.appendChild(libraryBtn);

    // Add container to song item
    Element.appendChild(actionContainer);

    // Add click event to navigate to music detail page
    Element.addEventListener("click", (e) => {
      // Only navigate if the click is not on the play button or library button
      if (
        !e.target.classList.contains("playListPlay") &&
        !e.target.classList.contains("add-to-library")
      ) {
        const songId = Element.querySelector(".playListPlay").id;
        window.location.href = `music-detail.html?id=${songId}`;
      }
    });
  }
);

// Scroll controls for popular songs
let left_scroll = document.getElementById("left_scroll");
let right_scroll = document.getElementById("right_scroll");
let pop_song = document.getElementsByClassName("pop_song")[0];

left_scroll.addEventListener("click", () => {
  pop_song.scrollLeft -= 330;
});
right_scroll.addEventListener("click", () => {
  pop_song.scrollLeft += 330;
});

// Scroll controls for artists
let left_scrolls = document.getElementById("left_scrolls");
let right_scrolls = document.getElementById("right_scrolls");
let item = document.getElementsByClassName("item")[0];

left_scrolls.addEventListener("click", () => {
  item.scrollLeft -= 330;
});
right_scrolls.addEventListener("click", () => {
  item.scrollLeft += 330;
});
