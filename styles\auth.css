@import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: var(--font-family);
}

body {
  background: var(--content-bg);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
}

body::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0.1;
  width: 100%;
  height: 100%;
  background: url("../images/—Pngtree—musical\ note\ staff\ abstract\ creative_1576172.png");
  background-position: center;
  z-index: -1;
}

nav {
  position: fixed;
  padding: 25px 60px;
  z-index: 1;
}

nav a img {
  width: 167px;
}

.form-wrapper {
  position: relative;
  border-radius: 10px;
  padding: 40px;
  width: 400px;
  background: rgba(17, 23, 39, 0.95);
  box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.3);
}

.form-wrapper h2 {
  color: #fff;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 25px;
}

.form-wrapper form {
  margin: 25px 0;
}

form .form-control {
  height: 50px;
  position: relative;
  margin-bottom: 16px;
}

.form-control input {
  height: 100%;
  width: 100%;
  background: rgba(76, 82, 98, 0.15);
  border: 1px solid rgba(76, 82, 98, 0.3);
  outline: none;
  border-radius: 4px;
  color: #fff;
  font-size: 14px;
  padding: 0 20px;
  transition: all 0.3s ease;
}

.form-control input:focus {
  background: rgba(76, 82, 98, 0.25);
  border-color: #36e2ec;
}

.form-control label {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 14px;
  pointer-events: none;
  color: #4c5262;
  transition: all 0.3s ease;
}

.form-control input:focus ~ label,
.form-control input:valid ~ label {
  top: 7px;
  font-size: 12px;
  color: #36e2ec;
}

form button {
  width: 100%;
  padding: 14px 0;
  font-size: 16px;
  background: #36e2ec;
  color: #111727;
  font-weight: 600;
  border-radius: 4px;
  border: none;
  outline: none;
  margin: 25px 0 10px;
  cursor: pointer;
  transition: 0.3s ease;
}

form button:hover {
  background: #2bc4cd;
}

.form-wrapper a {
  text-decoration: none;
  color: #36e2ec;
  transition: 0.3s ease;
}

.form-wrapper a:hover {
  text-decoration: underline;
  color: #2bc4cd;
}

.form-wrapper :where(p, small) {
  color: #4c5262;
  font-size: 14px;
}

form .form-help {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
}

form .remember-me {
  display: flex;
  align-items: center;
}

form .remember-me input {
  margin-right: 5px;
  accent-color: #36e2ec;
}

form .form-help :where(label, a) {
  font-size: 13px;
  color: #4c5262;
}

.form-wrapper p {
  margin-top: 15px;
  text-align: center;
}

.form-wrapper p a {
  color: #36e2ec;
  font-weight: 500;
}

.form-wrapper .logo {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
  color: #36e2ec;
}

.form-wrapper .logo i {
  font-size: 28px;
  margin-right: 10px;
}

.form-wrapper .logo span {
  font-size: 24px;
  font-weight: 600;
}

@media (max-width: 740px) {
  body::before {
    display: none;
  }

  nav,
  .form-wrapper {
    padding: 20px;
  }

  nav a img {
    width: 140px;
  }

  .form-wrapper {
    width: 100%;
    margin: 20px;
    padding: 30px 20px;
  }
}
