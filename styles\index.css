@import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap");
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

body {
  width: 100%;
  height: 100vh;
  background-color: var(--background-color);
  font-family: var(--font-family);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
header {
  width: 85%;
  height: 95%;
  /* border: 1px solid #fff; */
  display: flex;
  flex-wrap: wrap;
  overflow: hidden;
}
header .menu_side,
.song_side {
  position: relative;
  width: 25%;
  height: 90%;
  background-color: var(--sidebar-color);
  box-shadow: 5px 0px 2px #090f1f;
  color: var(--text-color);
}
header .song_side {
  width: 75%;
  background: var(--content-bg);
}
header .master_play {
  width: 100%;
  height: 10%;
  background: var(--sidebar-color);
  box-shadow: 0px 3px 0px #090f1f;
}
header .menu_side h1 {
  font-size: 20px;
  margin: 15px 0px 0px 20px;
  font-weight: 500;
}
header .menu_side .playlist {
  margin: 40px 0px 0px 20px;
}
header .menu_side .playlist h4 {
  font-size: 14px;
  font-weight: 400;
  padding: 10px;
  color: var(--text-muted);
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: var(--transition);
  border-radius: var(--border-radius-small);
}
header .menu_side .playlist h4:hover {
  color: var(--text-color);
  background-color: var(--hover-light);
}
header .menu_side .playlist h4 span {
  position: relative;
  margin-right: 35px;
}
header .menu_side .playlist h4 span::before {
  content: "";
  position: absolute;
  width: 4px;
  height: 4px;
  border: 2px solid var(--text-muted);
  border-radius: 50%;
  top: -4px;
  transition: var(--transition);
}
header .menu_side .playlist h4:hover span::before {
  border: 2px solid var(--text-color);
}
header .menu_side .playlist h4 .bi {
  display: none;
}
header .menu_side .playlist .active {
  color: var(--primary-color);
  background-color: var(--hover-light);
}
header .menu_side .playlist .active span {
  display: none;
}
header .menu_side .playlist .active .bi {
  display: flex;
  margin-right: 20px;
}
header .menu_side .playlist h4 a {
  color: inherit;
  text-decoration: none;
  display: flex;
  align-items: center;
  width: 100%;
}
header .menu_side .auth-links {
  margin: 20px 0px 0px 20px;
  padding-top: 20px;
  border-top: 1px solid var(--border-color);
}
header .menu_side .auth-links h4 {
  font-size: 14px;
  font-weight: 400;
  padding: 10px;
  cursor: pointer;
  transition: var(--transition);
  border-radius: var(--border-radius-small);
}
header .menu_side .auth-links h4:hover {
  background-color: var(--hover-light);
}
header .menu_side .auth-links h4 a {
  color: var(--text-muted);
  text-decoration: none;
  display: flex;
  align-items: center;
  transition: var(--transition);
  width: 100%;
}
header .menu_side .auth-links h4 a:hover {
  color: var(--primary-color);
}
header .menu_side .auth-links h4 a i {
  font-size: 16px;
  margin-right: 12px;
}
header .menu_side .menu_song {
  width: 100%;
  height: auto;
  max-height: calc(100% - 200px);
  margin-top: 15px;
  padding-bottom: 20px;
}
header .menu_side .menu_song li {
  position: relative;
  list-style-type: none;
  padding: 5px 0px 5px 20px;
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  cursor: pointer;
  transition: var(--transition, 0.3s linear);
  border-radius: 5px;
}
header .menu_side .menu_song li:last-child {
  margin-bottom: 0;
}
header .menu_side .menu_song li:hover {
  background: var(--hover-light);
}
header .menu_side .menu_song li span {
  font-size: 12px;
  font-weight: 600;
  color: var(--text-muted);
}
header .menu_side .menu_song li img {
  width: 32px;
  height: 32px;
  margin-left: 25px;
  border-radius: var(--border-radius-small);
  object-fit: cover;
}
header .menu_side .menu_song li h5 {
  font-size: 11px;
  margin-left: 15px;
  color: var(--text-color);
  max-width: 170px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
header .menu_side .menu_song li .subtitle {
  font-size: 9px;
  color: var(--text-muted);
}
header .menu_side .menu_song li .bi {
  position: absolute;
  right: 15px;
  top: 6px;
  font-size: 16px;
}

header .menu_side .menu_song li .add-to-library {
  right: 40px;
  color: var(--text-muted);
  transition: var(--transition);
}

header .menu_side .menu_song li .add-to-library:hover {
  color: var(--primary-color);
}

header .menu_side .menu_song li .add-to-library.bi-heart-fill {
  color: var(--primary-color);
}

header .master_play {
  display: flex;
  align-items: center;
  padding: 0px 20px;
}
header .master_play .wave {
  width: 30px;
  height: 30px;
  /* border: 1px solid #fff;*/
  padding-bottom: 5px;
  display: flex;
  align-items: flex-end;
  margin-right: 10px;
}
header .master_play .wave .wave1 {
  width: 3px;
  height: 10px;
  margin-right: 3px;
  border-radius: 10px 10px 0px 0px;
  background: #36e2ec;
  animation: unset;
}
header .master_play .wave .wave1:nth-child(2) {
  height: 13px;
  /* animation-delay: .4s; */
}
header .master_play .wave .wave1:nth-child(3) {
  height: 8px;
  /* animation-delay: .8s; */
}
/* javascript class wave */
header .master_play .active2 .wave1 {
  animation: wave 0.5s linear infinite;
}
header .master_play .active2 .wave1:nth-child(2) {
  animation-delay: 0.4s;
}
header .master_play .active2 .wave1:nth-child(3) {
  animation-delay: 0.8s;
}

@keyframes wave {
  0% {
    height: 10px;
  }
  50% {
    height: 15px;
  }
  100% {
    height: 10px;
  }
}

header .master_play img {
  width: 35px;
  height: 35px;
}
header .master_play h5 {
  width: 130px;
  font-size: 13px;
  margin-left: 15px;
  color: #fff;
  line-height: 17px;
}
header .master_play h5 .subtitle {
  font-size: 11px;
  color: #4c5262;
}
header .master_play .icon {
  font-size: 20px;
  color: #fff;
  margin: 0px 20px 0px 40px;
  outline: none;
  display: flex;
  align-items: center;
}

header .master_play .icon .bi {
  cursor: pointer;
  outline: none;
}
header .master_play .icon .shuffle {
  font-size: 17px;
  margin-right: 10px;
  display: flex;
  align-items: center;
  width: 18px;
  /* border: 1px solid #fff; */
  overflow: hidden;
}
header .master_play .icon #download_music {
  color: #fff;
}
header .master_play .icon #download_music .bi {
  font-size: 20px;
  margin-left: 10px;
}

header .master_play .icon .master-heart {
  font-size: 20px;
  margin-left: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #fff;
}

header .master_play .icon .master-heart:hover {
  color: var(--primary-color);
  transform: scale(1.1);
}

header .master_play .icon .master-heart.bi-heart-fill {
  color: var(--primary-color);
}

header .master_play .icon .bi:nth-child(3) {
  border: 1px solid rgb(105, 105, 170, 0.1);
  border-radius: 50%;
  padding: 1px 5px 0px 7px;
  margin: 0px 5px;
  transition: 0.3s linear;
}

header .master_play span {
  color: #fff;
  width: 32px;
  font-size: 11px;
  font-weight: 400;
}
header .master_play #currentStart {
  margin: 0px 0px 0px 20px;
}

header .master_play .bar {
  position: relative;
  width: 43%;
  height: 2px;
  background: rgb(105, 105, 170, 0.1);
  margin: 0px 15px 0px 10px;
}
header .master_play .bar .bar2 {
  position: absolute;
  background: #36e2ec;
  width: 0%;
  height: 100%;
  top: 0;
  transition: 1s linear;
}
header .master_play .bar .dot {
  position: absolute;
  width: 5px;
  height: 5px;
  background: #36e2ec;
  border-radius: 50%;
  left: 0%;
  top: -1px;
  transition: 1s linear;
}
header .master_play .bar .dot::before {
  content: "";
  position: absolute;
  width: 15px;
  height: 15px;
  border: 1px solid #36e2ec;
  border-radius: 50%;
  left: -6px;
  top: -6px;
  box-shadow: inset 0px 0px #36e2ec;
}
header .master_play .bar input {
  position: absolute;
  width: 100%;
  top: -6px;
  left: 0;
  cursor: pointer;
  z-index: 99999999;
  transition: 3s linear;
  opacity: 0;
}
header .master_play .vol {
  position: relative;
  width: 100px;
  height: 2px;
  margin-left: 50px;
  background: rgb(105, 105, 170, 0.1);
}
header .master_play .vol .bi {
  position: absolute;
  color: #fff;
  font-size: 25px;
  top: -17px;
  left: -30px;
}
header .master_play .vol input {
  position: absolute;
  width: 100%;
  top: -10px;
  left: 0;
  cursor: pointer;
  z-index: 99999999;
  transition: 3s linear;
  opacity: 0;
}

header .master_play .vol .vol_bar {
  position: absolute;
  background: #36e2ec;
  width: 0%;
  height: 100%;
  top: 0;
  transition: 1s linear;
}
header .master_play .vol .dot {
  position: absolute;
  width: 5px;
  height: 5px;
  background: #36e2ec;
  border-radius: 50%;
  left: 0%;
  top: -1px;
  transition: 1s linear;
}
header .master_play .vol .dot::before {
  content: "";
  position: absolute;
  width: 15px;
  height: 15px;
  border: 1px solid #36e2ec;
  border-radius: 50%;
  left: -6px;
  top: -6px;
  box-shadow: inset 0px 0px #36e2ec;
}

header .song_side {
  z-index: 2;
}
header .song_side::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 300px;
  background: url("./images/bg.png");
  z-index: -1;
}
header .song_side nav {
  width: 90%;
  height: 10%;
  margin: auto;
  /* border: 1px solid #fff; */
  display: flex;
  align-items: center;
  justify-content: space-between;
}
header .song_side nav ul {
  display: flex;
}
header .song_side nav ul li {
  position: relative;
  list-style-type: none;
  font-size: 13px;
  color: var(--text-muted);
  margin-right: 25px;
  cursor: pointer;
  transition: var(--transition);
  padding: 5px 0;
}

header .song_side nav ul li a {
  color: inherit;
  text-decoration: none;
  transition: var(--transition);
}

header .song_side nav ul li:hover,
header .song_side nav ul li a:hover {
  color: var(--text-color);
}

header .song_side nav ul li.active {
  color: var(--text-color);
}

header .song_side nav ul li span {
  position: absolute;
  width: 100%;
  height: 2.5px;
  background: var(--primary-color);
  bottom: -5px;
  left: 0;
  border-radius: 20px;
}
header .song_side nav .search {
  position: relative;
  width: 40%;
  padding: 1px 10px;
  border-radius: 20px;
  color: gray;
}
header .song_side nav .search::before {
  content: "";
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 20px;
  background: rgb(184, 184, 184, 0.1);
  backdrop-filter: blur(5px);
  z-index: -1;
}

header .song_side nav .search .bi {
  font-size: 13px;
  padding: 3px 0px 0px 10px;
}

header .song_side nav .search input {
  background: none;
  outline: none;
  border: none;
  padding: 0px 10px;
  color: var(--text-color);
  font-size: 12px;
}

header .song_side nav .search input::placeholder {
  color: var(--text-muted);
}

header .song_side nav .search .search_result {
  position: absolute;
  width: 100%;
  max-height: 200px;
  margin-top: 10px;
  border-radius: var(--border-radius);
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  flex-direction: column;
  overflow: auto;
  background-color: var(--sidebar-color);
  box-shadow: var(--box-shadow);
  border: 1px solid var(--border-color);
  z-index: var(--z-index-search);
}

header .song_side nav .search .search_result::-webkit-scrollbar {
  width: 6px;
}

header .song_side nav .search .search_result::-webkit-scrollbar-track {
  background: var(--hover-light);
  border-radius: 3px;
}

header .song_side nav .search .search_result::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 3px;
}

header .song_side nav .search .search_result .card {
  width: 100%;
  min-height: 45px;
  display: flex;
  align-items: center;
  padding: 8px 10px;
  text-decoration: none;
  background: var(--hover-light);
  margin-bottom: 2px;
  transition: var(--transition);
  border-radius: var(--border-radius-small);
}

header .song_side nav .search .search_result .card:hover {
  background: var(--hover-color);
}

header .song_side nav .search .search_result .card img {
  width: 35px;
  height: 35px;
  border-radius: var(--border-radius-small);
  object-fit: cover;
  margin-right: 10px;
}

header .song_side nav .search .search_result .card .content {
  flex: 1;
  padding: 0;
  font-size: 13px;
  font-weight: 600;
  color: var(--text-color);
  display: flex;
  align-items: flex-start;
  justify-content: center;
  flex-direction: column;
}

header .song_side nav .search .search_result .card .content .subtitle {
  font-size: 11px;
  color: var(--subtitle-color);
  font-weight: 500;
}

header .song_side nav .user {
  position: relative;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
}
header .song_side nav .user img {
  position: absolute;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  box-shadow: 2px 2px 8px #131312;
}

header .song_side .content {
  width: 90%;
  height: 30%;
  margin: auto;
  /* border: 10px solid #fff; */
  padding-top: 20px;
}
header .song_side .content h1 {
  font-size: 25px;
  font-weight: 600;
}
header .song_side .content p {
  font-size: 11px;
  font-weight: 400;
  color: #4c5262;
  margin: 5px;
}
header .song_side .content .buttons {
  margin-top: 15px;
}
header .song_side .content .buttons button {
  width: 130px;
  height: 30px;
  border: 2px solid #36e2ec;
  outline: none;
  border-radius: 20px;
  background: #5adae0;
  color: #fff;
  cursor: pointer;
  transition: 0.3s linear;
}
header .song_side .content .buttons button:hover {
  border: 2px solid #36e2ec;
  background: none;
  color: #36e2ec;
}
header .song_side .content .buttons button:nth-child(2):hover {
  border: 2px solid #36e2ec;
  background: #36e2ec;
  color: #fff;
}

header .song_side .popular_song {
  width: 90%;
  height: auto;
  /* border: 1px solid #fff; */
  margin: auto;
  margin-top: 15px;
}
header .song_side .popular_song .h4 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

header .song_side .popular_song .h4 .bi {
  color: #a4a8b4;
  cursor: pointer;
  transition: 0.3s linear;
}
header .song_side .popular_song .h4 .bi:hover {
  color: #fff;
}

header .song_side .popular_song .pop_song {
  width: 100%;
  height: 175px;
  margin-top: 15px;
  display: flex;
  overflow-x: auto;
  scroll-behavior: smooth;
  padding-bottom: 5px;
}
header .song_side .popular_song .pop_song::-webkit-scrollbar {
  height: 5px;
}
header .song_side .popular_song .pop_song::-webkit-scrollbar-track {
  background: var(--hover-light);
  border-radius: 10px;
}
header .song_side .popular_song .pop_song::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 10px;
}
header .song_side .popular_song .pop_song::-webkit-scrollbar-thumb:hover {
  background: var(--primary-hover);
}
header .song_side .popular_song .pop_song li {
  min-width: 100px;
  height: 140px;
  list-style-type: none;
  margin-right: 10px;
  transition: var(--transition);
  border-radius: var(--border-radius-small);
  padding: 5px;
  position: relative;
  cursor: pointer;
}
header .song_side .popular_song .pop_song li:hover {
  background: var(--hover-light);
  transform: translateY(-5px);
  box-shadow: var(--box-shadow-light);
}
header .song_side .popular_song .pop_song li .img_play {
  position: relative;
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}
header .song_side .popular_song .pop_song li .img_play img {
  width: 100%;
  height: 100%;
  border-radius: var(--border-radius-small);
  object-fit: cover;
}
header .song_side .popular_song .pop_song li .img_play .bi {
  position: absolute;
  font-size: 20px;
  cursor: pointer;
  transition: var(--transition);
  opacity: 0;
  color: var(--primary-color);
  filter: drop-shadow(0 0 4px rgba(0, 0, 0, 0.5));
}
header .song_side .popular_song .pop_song li .img_play:hover .bi {
  opacity: 1;
}
header .song_side .popular_song .pop_song li h5 {
  padding: 5px 0px 0px 5px;
  line-height: 15px;
  font-size: 10px;
  color: var(--text-color);
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
header .song_side .popular_song .pop_song li h5 .subtitle {
  font-size: 9px;
  color: var(--text-muted);
}

/* Heart icon styling for popular songs */
header .song_side .popular_song .pop_song li .add-to-library {
  position: absolute;
  top: 5px;
  right: 5px;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  background-color: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  width: 25px;
  height: 25px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(5px);
  opacity: 0;
  transform: scale(0.8);
}

header .song_side .popular_song .pop_song li:hover .add-to-library {
  opacity: 1;
  transform: scale(1);
}

header .song_side .popular_song .pop_song li .add-to-library:hover {
  color: var(--primary-color);
  transform: scale(1.1);
  background-color: rgba(0, 0, 0, 0.8);
}

header .song_side .popular_song .pop_song li .add-to-library.bi-heart-fill {
  color: var(--primary-color);
  background-color: rgba(54, 226, 236, 0.2);
  opacity: 1;
  transform: scale(1);
}

header .song_side .popular_song .pop_song li .add-to-library.in-library {
  opacity: 1;
  transform: scale(1);
}

/* Heart icon animation effects */
@keyframes heartBeat {
  0% {
    transform: scale(1);
  }
  25% {
    transform: scale(1.2);
  }
  50% {
    transform: scale(1);
  }
  75% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

.add-to-library.heart-animation {
  animation: heartBeat 0.6s ease-in-out;
}

header .song_side .popular_artists {
  width: 90%;
  height: auto;
  /* border: 1px solid #fff; */
  margin: auto;
  margin-top: 15px;
}
header .song_side .popular_artists .h4 {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

header .song_side .popular_artists .h4 .bi {
  color: #a4a8b4;
  cursor: pointer;
  transition: 0.3s linear;
}
header .song_side .popular_artists .h4 .bi:hover {
  color: #fff;
}
header .song_side .popular_artists .item {
  width: 100%;
  height: 70px;
  margin-top: 10px;
  display: flex;
  overflow-x: auto;
  scroll-behavior: smooth;
}
header .song_side .popular_artists .item::-webkit-scrollbar {
  height: 5px;
}
header .song_side .popular_artists .item::-webkit-scrollbar-track {
  background: var(--hover-light);
  border-radius: 10px;
}
header .song_side .popular_artists .item::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 10px;
}
header .song_side .popular_artists .item::-webkit-scrollbar-thumb:hover {
  background: var(--primary-hover);
}
header .song_side .popular_artists .item li {
  list-style-type: none;
  position: relative;
  min-width: 60px;
  height: 60px;
  border-radius: 50%;
  margin-right: 20px;
  cursor: pointer;
  transition: var(--transition);
}

header .song_side .popular_artists .item li:hover {
  transform: scale(1.1);
}

header .song_side .popular_artists .item li img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  position: absolute;
  object-fit: cover;
  box-shadow: var(--box-shadow-light);
  transition: var(--transition);
}

header .song_side .popular_artists .item li:hover img {
  box-shadow: var(--box-shadow);
}

header .menu_side {
  position: relative;
  width: 25%;
  height: 90%;
  /* border: 1px solid #fff; */
  background-color: #111727;
  box-shadow: 5px 0px 2px #090f1f;
  color: #fff;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #36e2ec rgba(76, 82, 98, 0.2);
}

/* Custom scrollbar styles for Webkit browsers (Chrome, Safari, etc.) */
header .menu_side::-webkit-scrollbar {
  width: 6px;
}

header .menu_side::-webkit-scrollbar-track {
  background: rgba(76, 82, 98, 0.2);
  border-radius: 3px;
}

header .menu_side::-webkit-scrollbar-thumb {
  background: #36e2ec;
  border-radius: 3px;
  transition: all 0.3s ease;
}

header .menu_side::-webkit-scrollbar-thumb:hover {
  background: #2bc4cd;
}
