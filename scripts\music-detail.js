// Music Detail Page JavaScript

document.addEventListener("DOMContentLoaded", () => {
  // Load player state first
  if (typeof loadPlayerState === "function") {
    loadPlayerState();
  }

  // Get song ID from URL parameter
  const urlParams = new URLSearchParams(window.location.search);
  const songId = urlParams.get("id");

  if (!songId) {
    // If no song ID is provided, redirect to home page
    window.location.href = "index.html";
    return;
  }

  // Find the song in the songs array
  const song = songs.find((song) => song.id === songId);

  if (!song) {
    // If song not found, show error message
    document.querySelector(".music-detail-container").innerHTML = `
            <div class="error-message">
                <h2>Bài hát không tồn tại</h2>
                <p>Bài hát bạn đang tìm kiếm không tồn tại hoặc đã bị xóa.</p>
                <a href="index.html" class="action-btn">
                    <i class="bi bi-house-door"></i> Về trang chủ
                </a>
            </div>
        `;
    return;
  }

  // Update page title
  document.title = `${extractSongName(song.songName)} - Music App`;

  // Update song details
  updateSongDetails(song);

  // Load related songs
  loadRelatedSongs(song);

  // Add event listeners
  setupEventListeners(song);
});

// Extract song name from HTML string
function extractSongName(songNameHtml) {
  // Create a temporary div to parse the HTML
  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = songNameHtml;

  // Get the text content (song name) without the subtitle
  const songName = tempDiv.childNodes[0].textContent.trim();
  return songName;
}

// Extract artist name from HTML string
function extractArtistName(songNameHtml) {
  // Create a temporary div to parse the HTML
  const tempDiv = document.createElement("div");
  tempDiv.innerHTML = songNameHtml;

  // Get the subtitle div content (artist name)
  const artistElement = tempDiv.querySelector(".subtitle");
  return artistElement ? artistElement.textContent.trim() : "Unknown Artist";
}

// Update song details in the UI
function updateSongDetails(song) {
  // Update song poster
  document.getElementById("detail-poster").src = song.poster;

  // Update song title and artist
  const songName = extractSongName(song.songName);
  const artistName = extractArtistName(song.songName);

  document.getElementById("detail-title").textContent = songName;
  document.getElementById("detail-artist").textContent = artistName;

  // Update download button
  const downloadBtn = document.getElementById("download-btn");
  downloadBtn.addEventListener("click", () => {
    const downloadLink = document.createElement("a");
    downloadLink.href = `./audio/${song.id}.mp3`;
    downloadLink.download = songName;
    downloadLink.click();
  });

  // Add some dummy description and lyrics for demonstration
  document.getElementById(
    "detail-description"
  ).textContent = `"${songName}" là một bài hát của ${artistName}. Bài hát này đã được phát hành và nhận được nhiều sự yêu thích từ người nghe. Với giai điệu cuốn hút và lời bài hát ý nghĩa, "${songName}" đã trở thành một trong những bài hát nổi bật của ${artistName}.`;

  document.getElementById(
    "detail-lyrics"
  ).textContent = `Đây là phần lời bài hát của "${songName}". Hiện tại chúng tôi chưa có lời bài hát này. Vui lòng quay lại sau.`;
}

// Load related songs
function loadRelatedSongs(currentSong) {
  const relatedSongsContainer = document.querySelector(
    ".related-songs-container"
  );

  // Get 4 random songs that are not the current song
  const relatedSongs = songs
    .filter((song) => song.id !== currentSong.id)
    .sort(() => 0.5 - Math.random())
    .slice(0, 4);

  // Add related songs to the container
  relatedSongsContainer.innerHTML = "";

  relatedSongs.forEach((song) => {
    const songElement = document.createElement("div");
    songElement.className = "related-song-item";
    songElement.innerHTML = `
            <img src="${song.poster}" alt="Related Song">
            <h5>${extractSongName(song.songName)}</h5>
            <div class="subtitle">${extractArtistName(song.songName)}</div>
        `;

    // Add click event to navigate to the song detail page
    songElement.addEventListener("click", () => {
      window.location.href = `music-detail.html?id=${song.id}`;
    });

    relatedSongsContainer.appendChild(songElement);
  });
}

// Setup event listeners
function setupEventListeners(song) {
  // Play button in the detail page
  const detailPlayBtn = document.getElementById("detail-play-btn");
  const playBtn = document.getElementById("play-btn");

  const playCurrentSong = () => {
    // Set the current song index
    index = parseInt(song.id);

    // Update the music player
    music.src = `./audio/${index}.mp3`;
    poster_master_play.src = `./styles/images/img/${index}.jpg`;

    // Update the title
    title.innerHTML = song.songName;

    // Update download link
    download_music.href = `./audio/${index}.mp3`;
    download_music.setAttribute("download", extractSongName(song.songName));

    // Play the song
    music.play();

    // Update UI
    masterPlay.classList.remove("bi-play-fill");
    masterPlay.classList.add("bi-pause-fill");

    // Add wave animation
    wave.classList.add("active2");

    // Make all play buttons to play icon
    makeAllPlays();

    // Save current player state
    saveCurrentPlayerState(song.id, 0, true);

    // Add to recently played history
    addSongToRecentlyPlayed(song.id);

    // Update master heart icon
    updateMasterHeartIcon();

    // Update the play button for current song
    document.getElementById(`${index}`).classList.remove("bi-play-circle-fill");
    document.getElementById(`${index}`).classList.add("bi-pause-circle-fill");

    // Update background
    makeAllBackgrounds();
    Array.from(document.getElementsByClassName("songItem"))[
      `${index - 1}`
    ].style.background = "rgb(105, 105, 170, .1)";

    // Add to recently played history
    if (typeof addSongToRecentlyPlayed === "function") {
      addSongToRecentlyPlayed(song.id);
    }

    // Update master heart icon
    if (typeof updateMasterHeartIcon === "function") {
      updateMasterHeartIcon();
    }
  };

  // Add click event to play buttons
  detailPlayBtn.addEventListener("click", playCurrentSong);
  playBtn.addEventListener("click", playCurrentSong);

  // Add to library button
  const addPlaylistBtn = document.getElementById("add-playlist-btn");

  // Heart button elements
  const detailHeartBtn = document.getElementById("detail-heart-btn");
  const detailHeartIcon = document.getElementById("detail-heart-icon");

  // Check if song is already in library and update both buttons
  if (isSongInLibrary(song.id)) {
    // Update playlist button text to show song is in library
    addPlaylistBtn.innerHTML =
      '<i class="bi bi-check-circle"></i> Đã thêm vào thư viện';
    addPlaylistBtn.classList.add("in-library");

    // Update heart button
    detailHeartIcon.classList.remove("bi-heart");
    detailHeartIcon.classList.add("bi-heart-fill");
    detailHeartBtn.innerHTML = '<i class="bi bi-heart-fill"></i> Đã yêu thích';
    detailHeartBtn.classList.add("in-library");
  }

  // Global function to update detail page heart icon (called from index.js)
  window.updateDetailPageHeartIcon = function (songId) {
    if (song.id === songId) {
      if (isSongInLibrary(songId)) {
        detailHeartIcon.classList.remove("bi-heart");
        detailHeartIcon.classList.add("bi-heart-fill");
        detailHeartBtn.innerHTML =
          '<i class="bi bi-heart-fill"></i> Đã yêu thích';
        detailHeartBtn.classList.add("in-library");

        // Add animation
        detailHeartIcon.classList.add("heart-animation");
        setTimeout(() => {
          detailHeartIcon.classList.remove("heart-animation");
        }, 600);
      } else {
        detailHeartIcon.classList.remove("bi-heart-fill");
        detailHeartIcon.classList.add("bi-heart");
        detailHeartBtn.innerHTML = '<i class="bi bi-heart"></i> Yêu thích';
        detailHeartBtn.classList.remove("in-library");
      }
    }
  };

  // Heart button click handler
  detailHeartBtn.addEventListener("click", () => {
    if (isSongInLibrary(song.id)) {
      // Remove from library
      if (removeSongFromLibrary(song.id)) {
        // Update heart button
        detailHeartIcon.classList.remove("bi-heart-fill");
        detailHeartIcon.classList.add("bi-heart");
        detailHeartBtn.innerHTML = '<i class="bi bi-heart"></i> Yêu thích';
        detailHeartBtn.classList.remove("in-library");

        // Update playlist button
        addPlaylistBtn.innerHTML =
          '<i class="bi bi-plus-circle"></i> Thêm vào thư viện';
        addPlaylistBtn.classList.remove("in-library");

        // Show success message
        if (typeof showNotification === "function") {
          showNotification("Đã xóa bài hát khỏi thư viện của bạn!", 3000);
        }

        // Sync all heart icons
        if (typeof syncAllHeartIcons === "function") {
          syncAllHeartIcons(song.id);
        }
      }
    } else {
      // Add to library
      if (addSongToLibrary(song.id)) {
        // Update heart button
        detailHeartIcon.classList.remove("bi-heart");
        detailHeartIcon.classList.add("bi-heart-fill");
        detailHeartBtn.innerHTML =
          '<i class="bi bi-heart-fill"></i> Đã yêu thích';
        detailHeartBtn.classList.add("in-library");

        // Update playlist button
        addPlaylistBtn.innerHTML =
          '<i class="bi bi-check-circle"></i> Đã thêm vào thư viện';
        addPlaylistBtn.classList.add("in-library");

        // Show success message
        if (typeof showNotification === "function") {
          showNotification("Thêm vào thư viện thành công", 3000);
        }

        // Sync all heart icons
        if (typeof syncAllHeartIcons === "function") {
          syncAllHeartIcons(song.id);
        }
      }
    }
  });

  addPlaylistBtn.addEventListener("click", () => {
    if (isSongInLibrary(song.id)) {
      // Remove from library
      if (removeSongFromLibrary(song.id)) {
        // Update button text
        addPlaylistBtn.innerHTML =
          '<i class="bi bi-plus-circle"></i> Thêm vào thư viện';
        addPlaylistBtn.classList.remove("in-library");

        // Update heart button
        detailHeartIcon.classList.remove("bi-heart-fill");
        detailHeartIcon.classList.add("bi-heart");
        detailHeartBtn.innerHTML = '<i class="bi bi-heart"></i> Yêu thích';
        detailHeartBtn.classList.remove("in-library");

        // Show success message
        if (typeof showNotification === "function") {
          showNotification("Đã xóa bài hát khỏi thư viện của bạn!", 3000);
        }

        // Sync all heart icons
        if (typeof syncAllHeartIcons === "function") {
          syncAllHeartIcons(song.id);
        }
      }
    } else {
      // Add to library
      if (addSongToLibrary(song.id)) {
        // Update button text
        addPlaylistBtn.innerHTML =
          '<i class="bi bi-check-circle"></i> Đã thêm vào thư viện';
        addPlaylistBtn.classList.add("in-library");

        // Update heart button
        detailHeartIcon.classList.remove("bi-heart");
        detailHeartIcon.classList.add("bi-heart-fill");
        detailHeartBtn.innerHTML =
          '<i class="bi bi-heart-fill"></i> Đã yêu thích';
        detailHeartBtn.classList.add("in-library");

        // Show success message
        if (typeof showNotification === "function") {
          showNotification("Thêm vào thư viện thành công", 3000);
        }

        // Sync all heart icons
        if (typeof syncAllHeartIcons === "function") {
          syncAllHeartIcons(song.id);
        }
      }
    }
  });

  // Add event listener to back button
  const backButton = document.querySelector(".back-button a");
  backButton.addEventListener("click", (e) => {
    e.preventDefault(); // Prevent default navigation

    // Set the current song to play in master_play
    index = parseInt(song.id);

    // Update the music player
    music.src = `./audio/${index}.mp3`;
    poster_master_play.src = `./styles/images/img/${index}.jpg`;

    // Update the title
    title.innerHTML = song.songName;

    // Update download link
    download_music.href = `./audio/${index}.mp3`;
    download_music.setAttribute("download", extractSongName(song.songName));

    // Play the song
    music.play();

    // Update UI
    masterPlay.classList.remove("bi-play-fill");
    masterPlay.classList.add("bi-pause-fill");

    // Add wave animation
    wave.classList.add("active2");

    // Add to recently played history
    if (typeof addSongToRecentlyPlayed === "function") {
      addSongToRecentlyPlayed(song.id);
    }

    // Update master heart icon
    if (typeof updateMasterHeartIcon === "function") {
      updateMasterHeartIcon();
    }

    // Navigate back to index.html
    window.location.href = "index.html";
  });
}
